# Page snapshot

```yaml
- generic [ref=e2]:
  - navigation [ref=e3]:
    - list [ref=e4]:
      - listitem [ref=e5]:
        - button "" [ref=e6] [cursor=pointer]:
          - generic [ref=e7] [cursor=pointer]: 
      - listitem [ref=e8]:
        - link " 首页" [ref=e9] [cursor=pointer]:
          - /url: /dashboard
          - generic [ref=e10] [cursor=pointer]: 
          - text: 首页
      - listitem [ref=e11]:
        - link " 租户管理" [ref=e12] [cursor=pointer]:
          - /url: /tenants
          - generic [ref=e13] [cursor=pointer]: 
          - text: 租户管理
      - listitem [ref=e14]:
        - link " 监控" [ref=e15] [cursor=pointer]:
          - /url: /monitoring
          - generic [ref=e16] [cursor=pointer]: 
          - text: 监控
      - listitem [ref=e17]:
        - link " 设置" [ref=e18] [cursor=pointer]:
          - /url: /settings
          - generic [ref=e19] [cursor=pointer]: 
          - text: 设置
    - generic [ref=e21]: 智慧养鹅 SaaS 管理平台
    - list [ref=e22]:
      - listitem [ref=e23]:
        - generic [ref=e26]:
          - searchbox "Search" [ref=e27]
          - button "" [ref=e29] [cursor=pointer]:
            - generic [ref=e30] [cursor=pointer]: 
      - listitem [ref=e31]:
        - button " 3" [ref=e32] [cursor=pointer]:
          - generic [ref=e33] [cursor=pointer]: 
          - generic [ref=e34] [cursor=pointer]: "3"
        - text:  
      - listitem [ref=e35]:
        - button "用户头像 admin" [ref=e36] [cursor=pointer]:
          - img "用户头像" [ref=e37] [cursor=pointer]
          - generic [ref=e38] [cursor=pointer]: admin
        - text:    
  - complementary [ref=e39]:
    - navigation [ref=e41]:
      - menu [ref=e42]:
        - listitem [ref=e43]:
          - link " 平台仪表盘" [ref=e44] [cursor=pointer]:
            - /url: /dashboard
            - generic [ref=e45] [cursor=pointer]: 
            - paragraph [ref=e46] [cursor=pointer]: 平台仪表盘
        - listitem [ref=e47]:
          - link " 租户管理 " [ref=e48] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e49] [cursor=pointer]: 
            - paragraph [ref=e50] [cursor=pointer]:
              - text: 租户管理
              - generic [ref=e51] [cursor=pointer]: 
          - list [ref=e52]:
            - listitem [ref=e53]:
              - link " 租户列表" [ref=e54] [cursor=pointer]:
                - /url: /tenants
                - generic [ref=e55] [cursor=pointer]: 
                - paragraph [ref=e56] [cursor=pointer]: 租户列表
            - listitem [ref=e57]:
              - link " 订阅管理" [ref=e58] [cursor=pointer]:
                - /url: /tenants/subscriptions
                - generic [ref=e59] [cursor=pointer]: 
                - paragraph [ref=e60] [cursor=pointer]: 订阅管理
            - listitem [ref=e61]:
              - link " 使用统计" [ref=e62] [cursor=pointer]:
                - /url: /tenants/usage
                - generic [ref=e63] [cursor=pointer]: 
                - paragraph [ref=e64] [cursor=pointer]: 使用统计
        - listitem [ref=e65]:
          - link " 今日鹅价 " [ref=e66] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e67] [cursor=pointer]: 
            - paragraph [ref=e68] [cursor=pointer]:
              - text: 今日鹅价
              - generic [ref=e69] [cursor=pointer]: 
          - text:   
        - listitem [ref=e70]:
          - link " 知识库管理 " [ref=e71] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e72] [cursor=pointer]: 
            - paragraph [ref=e73] [cursor=pointer]:
              - text: 知识库管理
              - generic [ref=e74] [cursor=pointer]: 
          - text:   
        - listitem [ref=e75]:
          - link " 公告管理 " [ref=e76] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e77] [cursor=pointer]: 
            - paragraph [ref=e78] [cursor=pointer]:
              - text: 公告管理
              - generic [ref=e79] [cursor=pointer]: 
          - text:  
        - listitem [ref=e80]:
          - link " 商城管理 " [ref=e81] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e82] [cursor=pointer]: 
            - paragraph [ref=e83] [cursor=pointer]:
              - text: 商城管理
              - generic [ref=e84] [cursor=pointer]: 
          - text:    
        - listitem [ref=e85]:
          - link " API管理 " [ref=e86] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e87] [cursor=pointer]: 
            - paragraph [ref=e88] [cursor=pointer]:
              - text: API管理
              - generic [ref=e89] [cursor=pointer]: 
          - text:   
        - listitem [ref=e90]:
          - link " 平台用户" [ref=e91] [cursor=pointer]:
            - /url: /platform-users
            - generic [ref=e92] [cursor=pointer]: 
            - paragraph [ref=e93] [cursor=pointer]: 平台用户
        - listitem [ref=e94]:
          - link " 统计报告 " [ref=e95] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e96] [cursor=pointer]: 
            - paragraph [ref=e97] [cursor=pointer]:
              - text: 统计报告
              - generic [ref=e98] [cursor=pointer]: 
          - text:   
        - listitem [ref=e99]:
          - link " 系统管理 " [ref=e100] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e101] [cursor=pointer]: 
            - paragraph [ref=e102] [cursor=pointer]:
              - text: 系统管理
              - generic [ref=e103] [cursor=pointer]: 
          - text:    
  - generic [ref=e104]:
    - generic [ref=e107]:
      - heading "租户管理" [level=1] [ref=e109]
      - list [ref=e111]:
        - listitem [ref=e112]:
          - link "首页" [ref=e113] [cursor=pointer]:
            - /url: /dashboard
        - listitem [ref=e114]: / 租户管理
    - generic [ref=e118]:
      - generic [ref=e119]:
        - generic [ref=e121]:
          - generic [ref=e122]:
            - heading "3" [level=3] [ref=e123]
            - paragraph [ref=e124]: 总租户数
          - generic [ref=e125]: 
        - generic [ref=e127]:
          - generic [ref=e128]:
            - heading "3" [level=3] [ref=e129]
            - paragraph [ref=e130]: 活跃租户
          - generic [ref=e131]: 
        - generic [ref=e133]:
          - generic [ref=e134]:
            - heading "0" [level=3] [ref=e135]
            - paragraph [ref=e136]: 总用户数
          - generic [ref=e137]: 
        - generic [ref=e139]:
          - generic [ref=e140]:
            - heading "¥0" [level=3] [ref=e141]
            - paragraph [ref=e142]: 总收入
          - generic [ref=e143]: $
      - generic [ref=e144]:
        - generic [ref=e146]:
          - heading " 租户管理" [level=5] [ref=e147]:
            - generic [ref=e148]: 
            - text: 租户管理
          - generic [ref=e149]:
            - link "+ 创建租户" [ref=e150] [cursor=pointer]:
              - /url: /tenants/create
              - generic [ref=e151] [cursor=pointer]: +
              - text: 创建租户
            - button " 全选" [ref=e152] [cursor=pointer]:
              - generic [ref=e153] [cursor=pointer]: 
              - text: 全选
            - button " 批量暂停" [disabled]:
              - generic: 
              - text: 批量暂停
            - button " 批量激活" [disabled]:
              - generic: 
              - text: 批量激活
            - button " 导出数据" [active] [ref=e154] [cursor=pointer]:
              - generic [ref=e155] [cursor=pointer]: 
              - text: 导出数据
            - link " 订阅管理" [ref=e156] [cursor=pointer]:
              - /url: /tenants/subscriptions
              - generic [ref=e157] [cursor=pointer]: 
              - text: 订阅管理
            - link " 使用统计" [ref=e158] [cursor=pointer]:
              - /url: /tenants/usage
              - generic [ref=e159] [cursor=pointer]: 
              - text: 使用统计
        - table [ref=e162]:
          - rowgroup [ref=e163]:
            - row "租户信息 类型 订阅计划 用户数 鹅群数 收入 状态 订阅到期 操作" [ref=e164]:
              - cell [ref=e165]:
                - checkbox [ref=e166]
              - cell "租户信息" [ref=e167]
              - cell "类型" [ref=e168]
              - cell "订阅计划" [ref=e169]
              - cell "用户数" [ref=e170]
              - cell "鹅群数" [ref=e171]
              - cell "收入" [ref=e172]
              - cell "状态" [ref=e173]
              - cell "订阅到期" [ref=e174]
              - cell "操作" [ref=e175]
          - rowgroup [ref=e176]:
            - 'row "代码: DEMO001 联系人: 电话: 13800138001 标准版 0 / 20 8 / 50 ¥0 活跃 2024/12/31 已过期 239 天" [ref=e177]':
              - cell [ref=e178]:
                - checkbox [ref=e179]
              - 'cell "代码: DEMO001 联系人: 电话: 13800138001" [ref=e180]':
                - generic [ref=e181]:
                  - strong
                  - generic [ref=e182]:
                    - text: "代码: DEMO001"
                    - text: "联系人:"
                    - text: "电话: 13800138001"
              - cell [ref=e183]
              - cell "标准版" [ref=e184]:
                - generic [ref=e185]: 标准版
              - cell "0 / 20" [ref=e186]:
                - generic [ref=e187]: "0"
                - generic [ref=e188]: / 20
              - cell "8 / 50" [ref=e189]:
                - generic [ref=e190]: "8"
                - generic [ref=e191]: / 50
              - cell "¥0" [ref=e192]:
                - strong [ref=e193]: ¥0
              - cell "活跃" [ref=e194]:
                - generic [ref=e195]: 活跃
              - cell "2024/12/31 已过期 239 天" [ref=e196]:
                - text: 2024/12/31
                - generic [ref=e197]: 已过期 239 天
              - cell [ref=e198]:
                - group [ref=e199]:
                  - link "" [ref=e200] [cursor=pointer]:
                    - /url: /tenants/1/details
                    - generic [ref=e201] [cursor=pointer]: 
                  - link "" [ref=e202] [cursor=pointer]:
                    - /url: /tenants/1/edit
                    - generic [ref=e203] [cursor=pointer]: 
            - 'row "代码: DEMO002 联系人: 电话: 13800138002 高级版 0 / 50 0 / 100 ¥0 活跃 2024/11/30 已过期 270 天" [ref=e204]':
              - cell [ref=e205]:
                - checkbox [ref=e206]
              - 'cell "代码: DEMO002 联系人: 电话: 13800138002" [ref=e207]':
                - generic [ref=e208]:
                  - strong
                  - generic [ref=e209]:
                    - text: "代码: DEMO002"
                    - text: "联系人:"
                    - text: "电话: 13800138002"
              - cell [ref=e210]
              - cell "高级版" [ref=e211]:
                - generic [ref=e212]: 高级版
              - cell "0 / 50" [ref=e213]:
                - generic [ref=e214]: "0"
                - generic [ref=e215]: / 50
              - cell "0 / 100" [ref=e216]:
                - generic [ref=e217]: "0"
                - generic [ref=e218]: / 100
              - cell "¥0" [ref=e219]:
                - strong [ref=e220]: ¥0
              - cell "活跃" [ref=e221]:
                - generic [ref=e222]: 活跃
              - cell "2024/11/30 已过期 270 天" [ref=e223]:
                - text: 2024/11/30
                - generic [ref=e224]: 已过期 270 天
              - cell [ref=e225]:
                - group [ref=e226]:
                  - link "" [ref=e227] [cursor=pointer]:
                    - /url: /tenants/2/details
                    - generic [ref=e228] [cursor=pointer]: 
                  - link "" [ref=e229] [cursor=pointer]:
                    - /url: /tenants/2/edit
                    - generic [ref=e230] [cursor=pointer]: 
            - 'row "代码: DEMO003 联系人: 电话: 13800138003 企业版 0 / 100 0 / 200 ¥0 活跃 2024/1/15 已过期 590 天" [ref=e231]':
              - cell [ref=e232]:
                - checkbox [ref=e233]
              - 'cell "代码: DEMO003 联系人: 电话: 13800138003" [ref=e234]':
                - generic [ref=e235]:
                  - strong
                  - generic [ref=e236]:
                    - text: "代码: DEMO003"
                    - text: "联系人:"
                    - text: "电话: 13800138003"
              - cell [ref=e237]
              - cell "企业版" [ref=e238]:
                - generic [ref=e239]: 企业版
              - cell "0 / 100" [ref=e240]:
                - generic [ref=e241]: "0"
                - generic [ref=e242]: / 100
              - cell "0 / 200" [ref=e243]:
                - generic [ref=e244]: "0"
                - generic [ref=e245]: / 200
              - cell "¥0" [ref=e246]:
                - strong [ref=e247]: ¥0
              - cell "活跃" [ref=e248]:
                - generic [ref=e249]: 活跃
              - cell "2024/1/15 已过期 590 天" [ref=e250]:
                - text: 2024/1/15
                - generic [ref=e251]: 已过期 590 天
              - cell [ref=e252]:
                - group [ref=e253]:
                  - link "" [ref=e254] [cursor=pointer]:
                    - /url: /tenants/3/details
                    - generic [ref=e255] [cursor=pointer]: 
                  - link "" [ref=e256] [cursor=pointer]:
                    - /url: /tenants/3/edit
                    - generic [ref=e257] [cursor=pointer]: 
  - contentinfo [ref=e258]:
    - strong [ref=e259]: Copyright © 2024 SAAS管理平台.
    - text: 版本 1.0.0
    - generic [ref=e260]:
      - generic [ref=e261]: "技术支持:"
      - text: Smart Goose Team
```