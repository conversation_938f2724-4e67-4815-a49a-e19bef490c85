{"summary": {"totalTests": 32, "passed": 26, "failed": 6, "duration": "89576ms", "timestamp": "2025-08-27T06:47:57.993Z"}, "results": [{"testName": "访问登录页面", "status": "PASS", "error": null, "screenshot": "screenshots/访问登录页面-after.png", "timestamp": "2025-08-27T06:48:06.811Z"}, {"testName": "登录功能", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:48:09.530Z"}, {"testName": "侧边栏切换按钮", "status": "PASS", "error": null, "screenshot": "screenshots/侧边栏切换按钮-after.png", "timestamp": "2025-08-27T06:48:12.599Z"}, {"testName": "仪表盘导航", "status": "PASS", "error": null, "screenshot": "screenshots/仪表盘导航-after.png", "timestamp": "2025-08-27T06:48:15.050Z"}, {"testName": "租户管理导航", "status": "PASS", "error": null, "screenshot": "screenshots/租户管理导航-after.png", "timestamp": "2025-08-27T06:48:17.477Z"}, {"testName": "监控导航", "status": "PASS", "error": null, "screenshot": "screenshots/监控导航-after.png", "timestamp": "2025-08-27T06:48:19.934Z"}, {"testName": "设置导航", "status": "PASS", "error": null, "screenshot": "screenshots/设置导航-after.png", "timestamp": "2025-08-27T06:48:22.322Z"}, {"testName": "创建租户按钮", "status": "PASS", "error": null, "screenshot": "screenshots/创建租户按钮-after.png", "timestamp": "2025-08-27T06:48:25.315Z"}, {"testName": "全选按钮", "status": "PASS", "error": null, "screenshot": "screenshots/全选按钮-after.png", "timestamp": "2025-08-27T06:48:28.222Z"}, {"testName": "批量暂停按钮", "status": "PASS", "error": null, "screenshot": "screenshots/批量暂停按钮-after.png", "timestamp": "2025-08-27T06:48:31.100Z"}, {"testName": "批量激活按钮", "status": "PASS", "error": null, "screenshot": "screenshots/批量激活按钮-after.png", "timestamp": "2025-08-27T06:48:34.006Z"}, {"testName": "导出数据按钮", "status": "PASS", "error": null, "screenshot": "screenshots/导出数据按钮-after.png", "timestamp": "2025-08-27T06:48:36.899Z"}, {"testName": "订阅管理按钮", "status": "PASS", "error": null, "screenshot": "screenshots/订阅管理按钮-after.png", "timestamp": "2025-08-27T06:48:39.969Z"}, {"testName": "使用统计按钮", "status": "PASS", "error": null, "screenshot": "screenshots/使用统计按钮-after.png", "timestamp": "2025-08-27T06:48:42.959Z"}, {"testName": "查看详情按钮(第一个)", "status": "PASS", "error": null, "screenshot": "screenshots/查看详情按钮(第一个)-after.png", "timestamp": "2025-08-27T06:48:46.010Z"}, {"testName": "编辑按钮(第一个)", "status": "PASS", "error": null, "screenshot": "screenshots/编辑按钮(第一个)-after.png", "timestamp": "2025-08-27T06:48:49.140Z"}, {"testName": "取消按钮", "status": "PASS", "error": null, "screenshot": "screenshots/取消按钮-after.png", "timestamp": "2025-08-27T06:48:52.905Z"}, {"testName": "创建租户提交按钮", "status": "PASS", "error": null, "screenshot": "screenshots/创建租户提交按钮-after.png", "timestamp": "2025-08-27T06:48:55.895Z"}, {"testName": "返回列表按钮", "status": "PASS", "error": null, "screenshot": "screenshots/返回列表按钮-after.png", "timestamp": "2025-08-27T06:48:58.264Z"}, {"testName": "计划筛选下拉框", "status": "PASS", "error": null, "screenshot": "screenshots/计划筛选下拉框-after.png", "timestamp": "2025-08-27T06:49:02.619Z"}, {"testName": "状态筛选下拉框", "status": "PASS", "error": null, "screenshot": "screenshots/状态筛选下拉框-after.png", "timestamp": "2025-08-27T06:49:05.112Z"}, {"testName": "续费按钮(第一个)", "status": "PASS", "error": null, "screenshot": "screenshots/续费按钮(第一个)-after.png", "timestamp": "2025-08-27T06:49:07.520Z"}, {"testName": "变更订阅按钮(第一个)", "status": "FAIL", "error": "page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button[onclick*=\"changeSubscription\"]:first-child') to be visible\u001b[22m\n", "screenshot": "screenshots/变更订阅按钮(第一个)-error.png", "timestamp": "2025-08-27T06:49:12.641Z"}, {"testName": "JS函数-全选租户函数", "status": "FAIL", "error": "page.evaluate: ReferenceError: selectAllTenants is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "screenshot": null, "timestamp": "2025-08-27T06:49:16.529Z"}, {"testName": "JS函数-切换全选函数", "status": "FAIL", "error": "page.evaluate: ReferenceError: toggleSelectAllTenants is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "screenshot": null, "timestamp": "2025-08-27T06:49:16.531Z"}, {"testName": "JS函数-获取选中租户ID函数", "status": "FAIL", "error": "page.evaluate: ReferenceError: getSelectedTenantIds is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "screenshot": null, "timestamp": "2025-08-27T06:49:16.532Z"}, {"testName": "JS函数-批量更新状态函数", "status": "FAIL", "error": "page.evaluate: ReferenceError: batchUpdateTenantStatus is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "screenshot": null, "timestamp": "2025-08-27T06:49:16.534Z"}, {"testName": "JS函数-导出租户函数", "status": "FAIL", "error": "page.evaluate: ReferenceError: exportTenants is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)", "screenshot": null, "timestamp": "2025-08-27T06:49:16.535Z"}, {"testName": "控制台错误检查-http://localhost:4001", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:49:19.591Z"}, {"testName": "控制台错误检查-http://localhost:4001/tenants", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:49:22.264Z"}, {"testName": "控制台错误检查-http://localhost:4001/tenants/create", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:49:24.875Z"}, {"testName": "控制台错误检查-http://localhost:4001/tenants/subscriptions", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:49:27.552Z"}]}