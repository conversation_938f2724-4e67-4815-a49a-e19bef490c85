
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理按钮测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .error { background-color: #ffe6e6; }
    </style>
</head>
<body>
    <h1>后台管理按钮功能测试报告</h1>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <p><strong>总测试数:</strong> 32</p>
        <p><strong>通过:</strong> <span class="pass">26</span></p>
        <p><strong>失败:</strong> <span class="fail">6</span></p>
        <p><strong>测试时间:</strong> 89576ms</p>
        <p><strong>执行时间:</strong> 2025-08-27T06:47:57.993Z</p>
    </div>

    <h2>详细结果</h2>
    <table>
        <thead>
            <tr>
                <th>测试名称</th>
                <th>状态</th>
                <th>错误信息</th>
                <th>时间戳</th>
            </tr>
        </thead>
        <tbody>
            
                <tr class="">
                    <td>访问登录页面</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:06.811Z</td>
                </tr>
            
                <tr class="">
                    <td>登录功能</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:09.530Z</td>
                </tr>
            
                <tr class="">
                    <td>侧边栏切换按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:12.599Z</td>
                </tr>
            
                <tr class="">
                    <td>仪表盘导航</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:15.050Z</td>
                </tr>
            
                <tr class="">
                    <td>租户管理导航</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:17.477Z</td>
                </tr>
            
                <tr class="">
                    <td>监控导航</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:19.934Z</td>
                </tr>
            
                <tr class="">
                    <td>设置导航</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:22.322Z</td>
                </tr>
            
                <tr class="">
                    <td>创建租户按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:25.315Z</td>
                </tr>
            
                <tr class="">
                    <td>全选按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:28.222Z</td>
                </tr>
            
                <tr class="">
                    <td>批量暂停按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:31.100Z</td>
                </tr>
            
                <tr class="">
                    <td>批量激活按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:34.006Z</td>
                </tr>
            
                <tr class="">
                    <td>导出数据按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:36.899Z</td>
                </tr>
            
                <tr class="">
                    <td>订阅管理按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:39.969Z</td>
                </tr>
            
                <tr class="">
                    <td>使用统计按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:42.959Z</td>
                </tr>
            
                <tr class="">
                    <td>查看详情按钮(第一个)</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:46.010Z</td>
                </tr>
            
                <tr class="">
                    <td>编辑按钮(第一个)</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:49.140Z</td>
                </tr>
            
                <tr class="">
                    <td>取消按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:52.905Z</td>
                </tr>
            
                <tr class="">
                    <td>创建租户提交按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:55.895Z</td>
                </tr>
            
                <tr class="">
                    <td>返回列表按钮</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:48:58.264Z</td>
                </tr>
            
                <tr class="">
                    <td>计划筛选下拉框</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:49:02.619Z</td>
                </tr>
            
                <tr class="">
                    <td>状态筛选下拉框</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:49:05.112Z</td>
                </tr>
            
                <tr class="">
                    <td>续费按钮(第一个)</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:49:07.520Z</td>
                </tr>
            
                <tr class="error">
                    <td>变更订阅按钮(第一个)</td>
                    <td class="fail">FAIL</td>
                    <td>page.waitForSelector: Timeout 5000ms exceeded.
Call log:
[2m  - waiting for locator('button[onclick*="changeSubscription"]:first-child') to be visible[22m
</td>
                    <td>2025-08-27T06:49:12.641Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-全选租户函数</td>
                    <td class="fail">FAIL</td>
                    <td>page.evaluate: ReferenceError: selectAllTenants is not defined
    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)
    at eval (eval at evaluate (:291:30), <anonymous>:3:67)
    at UtilityScript.evaluate (<anonymous>:293:16)
    at UtilityScript.<anonymous> (<anonymous>:1:44)</td>
                    <td>2025-08-27T06:49:16.529Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-切换全选函数</td>
                    <td class="fail">FAIL</td>
                    <td>page.evaluate: ReferenceError: toggleSelectAllTenants is not defined
    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)
    at eval (eval at evaluate (:291:30), <anonymous>:3:67)
    at UtilityScript.evaluate (<anonymous>:293:16)
    at UtilityScript.<anonymous> (<anonymous>:1:44)</td>
                    <td>2025-08-27T06:49:16.531Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-获取选中租户ID函数</td>
                    <td class="fail">FAIL</td>
                    <td>page.evaluate: ReferenceError: getSelectedTenantIds is not defined
    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)
    at eval (eval at evaluate (:291:30), <anonymous>:3:67)
    at UtilityScript.evaluate (<anonymous>:293:16)
    at UtilityScript.<anonymous> (<anonymous>:1:44)</td>
                    <td>2025-08-27T06:49:16.532Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-批量更新状态函数</td>
                    <td class="fail">FAIL</td>
                    <td>page.evaluate: ReferenceError: batchUpdateTenantStatus is not defined
    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)
    at eval (eval at evaluate (:291:30), <anonymous>:3:67)
    at UtilityScript.evaluate (<anonymous>:293:16)
    at UtilityScript.<anonymous> (<anonymous>:1:44)</td>
                    <td>2025-08-27T06:49:16.534Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-导出租户函数</td>
                    <td class="fail">FAIL</td>
                    <td>page.evaluate: ReferenceError: exportTenants is not defined
    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)
    at eval (eval at evaluate (:291:30), <anonymous>:3:67)
    at UtilityScript.evaluate (<anonymous>:293:16)
    at UtilityScript.<anonymous> (<anonymous>:1:44)</td>
                    <td>2025-08-27T06:49:16.535Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:49:19.591Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001/tenants</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:49:22.264Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001/tenants/create</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:49:24.875Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001/tenants/subscriptions</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:49:27.552Z</td>
                </tr>
            
        </tbody>
    </table>
</body>
</html>