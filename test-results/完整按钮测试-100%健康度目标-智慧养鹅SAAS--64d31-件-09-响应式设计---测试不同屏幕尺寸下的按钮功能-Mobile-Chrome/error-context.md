# Page snapshot

```yaml
- generic [ref=e2]:
  - navigation [ref=e3]:
    - list [ref=e4]:
      - listitem [ref=e5]:
        - button "" [active] [ref=e6] [cursor=pointer]:
          - generic [ref=e7] [cursor=pointer]: 
      - listitem [ref=e8]:
        - link " 首页" [ref=e9] [cursor=pointer]:
          - /url: /dashboard
          - generic [ref=e10] [cursor=pointer]: 
          - text: 首页
      - listitem [ref=e11]:
        - link " 租户管理" [ref=e12] [cursor=pointer]:
          - /url: /tenants
          - generic [ref=e13] [cursor=pointer]: 
          - text: 租户管理
      - text:  
    - list [ref=e14]:
      - listitem: 
      - listitem [ref=e15]:
        - button " 3" [ref=e16] [cursor=pointer]:
          - generic [ref=e17] [cursor=pointer]: 
          - generic [ref=e18] [cursor=pointer]: "3"
        - text:  
      - listitem [ref=e19]:
        - button "用户头像" [ref=e20] [cursor=pointer]:
          - img "用户头像" [ref=e21] [cursor=pointer]
        - text:    
  - complementary [ref=e22]:
    - navigation [ref=e24]:
      - menu [ref=e25]:
        - listitem [ref=e26]:
          - link " 平台仪表盘" [ref=e27] [cursor=pointer]:
            - /url: /dashboard
            - generic [ref=e28] [cursor=pointer]: 
            - paragraph [ref=e29] [cursor=pointer]: 平台仪表盘
        - listitem [ref=e30]:
          - link " 租户管理 " [ref=e31] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e32] [cursor=pointer]: 
            - paragraph [ref=e33] [cursor=pointer]:
              - text: 租户管理
              - generic [ref=e34] [cursor=pointer]: 
          - text:   
        - listitem [ref=e35]:
          - link " 今日鹅价 " [ref=e36] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e37] [cursor=pointer]: 
            - paragraph [ref=e38] [cursor=pointer]:
              - text: 今日鹅价
              - generic [ref=e39] [cursor=pointer]: 
          - text:   
        - listitem [ref=e40]:
          - link " 知识库管理 " [ref=e41] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e42] [cursor=pointer]: 
            - paragraph [ref=e43] [cursor=pointer]:
              - text: 知识库管理
              - generic [ref=e44] [cursor=pointer]: 
          - text:   
        - listitem [ref=e45]:
          - link " 公告管理 " [ref=e46] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e47] [cursor=pointer]: 
            - paragraph [ref=e48] [cursor=pointer]:
              - text: 公告管理
              - generic [ref=e49] [cursor=pointer]: 
          - text:  
        - listitem [ref=e50]:
          - link " 商城管理 " [ref=e51] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e52] [cursor=pointer]: 
            - paragraph [ref=e53] [cursor=pointer]:
              - text: 商城管理
              - generic [ref=e54] [cursor=pointer]: 
          - text:    
        - listitem [ref=e55]:
          - link " API管理 " [ref=e56] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e57] [cursor=pointer]: 
            - paragraph [ref=e58] [cursor=pointer]:
              - text: API管理
              - generic [ref=e59] [cursor=pointer]: 
          - text:   
        - listitem [ref=e60]:
          - link " 平台用户" [ref=e61] [cursor=pointer]:
            - /url: /platform-users
            - generic [ref=e62] [cursor=pointer]: 
            - paragraph [ref=e63] [cursor=pointer]: 平台用户
        - listitem [ref=e64]:
          - link " 统计报告 " [ref=e65] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e66] [cursor=pointer]: 
            - paragraph [ref=e67] [cursor=pointer]:
              - text: 统计报告
              - generic [ref=e68] [cursor=pointer]: 
          - text:   
        - listitem [ref=e69]:
          - link " 系统管理 " [ref=e70] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e71] [cursor=pointer]: 
            - paragraph [ref=e72] [cursor=pointer]:
              - text: 系统管理
              - generic [ref=e73] [cursor=pointer]: 
          - text:    
  - generic [ref=e74]:
    - generic [ref=e77]:
      - heading "平台仪表盘" [level=1] [ref=e79]
      - list [ref=e81]:
        - listitem [ref=e82]:
          - link "首页" [ref=e83] [cursor=pointer]:
            - /url: /dashboard
        - listitem [ref=e84]: / 平台仪表盘
    - generic [ref=e86]:
      - generic [ref=e87]:
        - generic [ref=e89]:
          - generic [ref=e90]:
            - heading "3" [level=3] [ref=e91]
            - paragraph [ref=e92]: 总租户数
          - generic [ref=e93]: 
          - link "查看详情 " [ref=e94] [cursor=pointer]:
            - /url: /tenants
            - text: 查看详情
            - generic [ref=e95] [cursor=pointer]: 
        - generic [ref=e97]:
          - generic [ref=e98]:
            - heading "3" [level=3] [ref=e99]
            - paragraph [ref=e100]: 活跃租户
          - generic [ref=e101]: 
          - link "查看详情 " [ref=e102] [cursor=pointer]:
            - /url: /tenants?status=active
            - text: 查看详情
            - generic [ref=e103] [cursor=pointer]: 
        - generic [ref=e105]:
          - generic [ref=e106]:
            - heading "1" [level=3] [ref=e107]
            - paragraph [ref=e108]: 平台用户总数
          - generic [ref=e109]: 
          - link "查看详情 " [ref=e110] [cursor=pointer]:
            - /url: /platform-users
            - text: 查看详情
            - generic [ref=e111] [cursor=pointer]: 
        - generic [ref=e113]:
          - generic [ref=e114]:
            - heading "¥407.5" [level=3] [ref=e115]
            - paragraph [ref=e116]: 本月收入
          - generic [ref=e117]: $
          - link "查看详情 " [ref=e118] [cursor=pointer]:
            - /url: /mall/orders
            - text: 查看详情
            - generic [ref=e119] [cursor=pointer]: 
      - generic [ref=e120]:
        - generic [ref=e122]:
          - generic [ref=e123]:
            - heading "0" [level=3] [ref=e124]
            - paragraph [ref=e125]: 基础版租户
          - generic [ref=e126]: 
        - generic [ref=e128]:
          - generic [ref=e129]:
            - heading "1" [level=3] [ref=e130]
            - paragraph [ref=e131]: 标准版租户
          - generic [ref=e132]: 
        - generic [ref=e134]:
          - generic [ref=e135]:
            - heading "1" [level=3] [ref=e136]
            - paragraph [ref=e137]: 高级版租户
          - generic [ref=e138]: 
        - generic [ref=e140]:
          - generic [ref=e141]:
            - heading "1" [level=3] [ref=e142]
            - paragraph [ref=e143]: 企业版租户
          - generic [ref=e144]: 
      - generic [ref=e145]:
        - generic [ref=e147]:
          - generic [ref=e149]: 
          - generic [ref=e150]:
            - generic [ref=e151]: 鹅群总数
            - generic [ref=e152]: "8"
        - generic [ref=e154]:
          - generic [ref=e156]: 
          - generic [ref=e157]:
            - generic [ref=e158]: 鹅只总数
            - generic [ref=e159]: "920"
        - generic [ref=e161]:
          - generic [ref=e163]: 
          - generic [ref=e164]:
            - generic [ref=e165]: 今日记录
            - generic [ref=e166]: "0"
        - generic [ref=e168]:
          - generic [ref=e170]: 
          - generic [ref=e171]:
            - generic [ref=e172]: 今日产蛋
            - generic [ref=e173]: "0"
        - generic [ref=e175]:
          - generic [ref=e177]: 
          - generic [ref=e178]:
            - generic [ref=e179]: 商品总数
            - generic [ref=e180]: "12"
        - generic [ref=e182]:
          - generic [ref=e184]: 
          - generic [ref=e185]:
            - generic [ref=e186]: 今日鹅价
            - generic [ref=e187]: "0"
      - generic [ref=e188]:
        - generic [ref=e190]:
          - generic [ref=e191]:
            - heading " 系统状态监控" [level=3] [ref=e192]:
              - generic [ref=e193]: 
              - text: 系统状态监控
            - button "" [ref=e195] [cursor=pointer]:
              - generic [ref=e196] [cursor=pointer]: 
          - generic [ref=e197]:
            - generic [ref=e198]:
              - generic [ref=e200]:
                - generic [ref=e202]: 
                - generic [ref=e203]:
                  - generic [ref=e204]: 数据库
                  - generic [ref=e205]: 正常
              - generic [ref=e209]:
                - generic [ref=e211]: 
                - generic [ref=e212]:
                  - generic [ref=e213]: 即将到期
                  - generic [ref=e214]: "0"
              - generic [ref=e217]:
                - generic [ref=e219]: 
                - generic [ref=e220]:
                  - generic [ref=e221]: 待处理订单
                  - generic [ref=e222]: "1"
              - generic [ref=e226]:
                - generic [ref=e228]: 
                - generic [ref=e229]:
                  - generic [ref=e230]: API端点
                  - generic [ref=e231]: 10/10
            - generic [ref=e234]:
              - generic [ref=e236]:
                - text: CPU 使用率
                - generic [ref=e237]:
                  - generic [ref=e238]: "78"
                  - text: /100%
              - generic [ref=e242]:
                - text: 内存使用率
                - generic [ref=e243]:
                  - generic [ref=e244]: "65"
                  - text: /100%
              - generic [ref=e248]:
                - text: 磁盘使用率
                - generic [ref=e249]:
                  - generic [ref=e250]: "23"
                  - text: /100%
            - generic [ref=e253]:
              - generic [ref=e255]:
                - generic [ref=e256]: 
                - text: "运行时间: 0小时 24分钟"
              - generic [ref=e258]:
                - generic [ref=e259]: 
                - text: "Node.js: v23.11.0"
        - generic [ref=e261]:
          - heading "用户活跃度" [level=3] [ref=e263]: 用户活跃度
          - generic [ref=e264]:
            - generic [ref=e266]:
              - generic [ref=e267]:
                - text: 周活跃用户
                - generic [ref=e268]:
                  - generic [ref=e269]: "1"
                  - text: /1
              - generic [ref=e272]:
                - text: 月活跃用户
                - generic [ref=e273]:
                  - generic [ref=e274]: "1"
                  - text: /1
              - generic [ref=e277]:
                - text: 活跃用户
                - generic [ref=e278]:
                  - generic [ref=e279]: "1"
                  - text: /1
            - generic [ref=e282]:
              - generic [ref=e283]:
                - generic [ref=e284]: 
                - text: "平台知识库: 12篇文章"
              - generic [ref=e285]:
                - generic [ref=e286]: 
                - text: "活跃公告: 12条"
      - generic [ref=e287]:
        - generic [ref=e289]:
          - generic [ref=e290]:
            - heading " 最新租户" [level=3] [ref=e291]:
              - generic [ref=e292]: 
              - text: 最新租户
            - link "" [ref=e294] [cursor=pointer]:
              - /url: /tenants
              - generic [ref=e295] [cursor=pointer]: 
          - table [ref=e298]:
            - rowgroup [ref=e299]:
              - row "租户名称 联系人 用户数 鹅群数 订阅计划" [ref=e300]:
                - cell "租户名称" [ref=e301]
                - cell "联系人" [ref=e302]
                - cell "用户数" [ref=e303]
                - cell "鹅群数" [ref=e304]
                - cell "订阅计划" [ref=e305]
            - rowgroup [ref=e306]:
              - row "DEMO001 0 8 标准版" [ref=e307]:
                - cell "DEMO001" [ref=e308]:
                  - strong
                  - generic [ref=e309]: DEMO001
                - cell [ref=e310]
                - cell "0" [ref=e311]:
                  - generic [ref=e312]: "0"
                - cell "8" [ref=e313]:
                  - generic [ref=e314]: "8"
                - cell "标准版" [ref=e315]:
                  - generic [ref=e316]: 标准版
              - row "DEMO002 0 0 高级版" [ref=e317]:
                - cell "DEMO002" [ref=e318]:
                  - strong
                  - generic [ref=e319]: DEMO002
                - cell [ref=e320]
                - cell "0" [ref=e321]:
                  - generic [ref=e322]: "0"
                - cell "0" [ref=e323]:
                  - generic [ref=e324]: "0"
                - cell "高级版" [ref=e325]:
                  - generic [ref=e326]: 高级版
              - row "DEMO003 0 0 企业版" [ref=e327]:
                - cell "DEMO003" [ref=e328]:
                  - strong
                  - generic [ref=e329]: DEMO003
                - cell [ref=e330]
                - cell "0" [ref=e331]:
                  - generic [ref=e332]: "0"
                - cell "0" [ref=e333]:
                  - generic [ref=e334]: "0"
                - cell "企业版" [ref=e335]:
                  - generic [ref=e336]: 企业版
        - generic [ref=e338]:
          - generic [ref=e339]:
            - heading " 最新订单" [level=3] [ref=e340]:
              - generic [ref=e341]: 
              - text: 最新订单
            - link "" [ref=e343] [cursor=pointer]:
              - /url: /mall/orders
              - generic [ref=e344] [cursor=pointer]: 
          - table [ref=e347]:
            - rowgroup [ref=e348]:
              - row "订单号 租户 金额 状态 时间" [ref=e349]:
                - cell "订单号" [ref=e350]
                - cell "租户" [ref=e351]
                - cell "金额" [ref=e352]
                - cell "状态" [ref=e353]
                - cell "时间" [ref=e354]
            - rowgroup [ref=e355]:
              - row "示例养殖场A 系统管理员 ¥NaN 待处理 8月26日 20:49" [ref=e356]:
                - cell [ref=e357]
                - cell "示例养殖场A 系统管理员" [ref=e358]:
                  - text: 示例养殖场A
                  - generic [ref=e359]: 系统管理员
                - cell "¥NaN" [ref=e360]:
                  - strong [ref=e361]: ¥NaN
                - cell "待处理" [ref=e362]:
                  - generic [ref=e363]: 待处理
                - cell "8月26日 20:49" [ref=e364]:
                  - generic [ref=e365]: 8月26日 20:49
              - row "示例养殖场A 系统管理员 ¥NaN 已完成 8月26日 20:49" [ref=e366]:
                - cell [ref=e367]
                - cell "示例养殖场A 系统管理员" [ref=e368]:
                  - text: 示例养殖场A
                  - generic [ref=e369]: 系统管理员
                - cell "¥NaN" [ref=e370]:
                  - strong [ref=e371]: ¥NaN
                - cell "已完成" [ref=e372]:
                  - generic [ref=e373]: 已完成
                - cell "8月26日 20:49" [ref=e374]:
                  - generic [ref=e375]: 8月26日 20:49
  - contentinfo [ref=e376]:
    - strong [ref=e377]: Copyright © 2024 SAAS管理平台.
    - text: 版本 1.0.0
    - generic [ref=e378]:
      - generic [ref=e379]: "技术支持:"
      - text: Smart Goose Team
```