{"config": {"configFile": "/Volumes/DATA/千问/智慧养鹅全栈/playwright.config.js", "rootDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-setup.js", "globalTeardown": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 4, "webServer": null}, "suites": [{"title": "admin-buttons-test.spec.js", "file": "admin-buttons-test.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "后台管理中心按钮功能测试", "file": "admin-buttons-test.spec.js", "line": 190, "column": 6, "specs": [{"title": "访问登录页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5341, "errors": [], "stdout": [{"text": "开始测试后台管理系统按钮功能...\n"}, {"text": "测试按钮: 访问登录页面\n"}, {"text": "✅ 访问登录页面 - 测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:55:57.810Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-13fe31ce9fc7dccd15d9", "file": "admin-buttons-test.spec.js", "line": 215, "column": 3}, {"title": "测试登录功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2733, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:56:08.594Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-1064d3bc3f5a054e3e11", "file": "admin-buttons-test.spec.js", "line": 222, "column": 3}, {"title": "测试导航菜单按钮", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 13021, "errors": [], "stdout": [{"text": "测试按钮: 侧边栏切换按钮\n"}, {"text": "✅ 侧边栏切换按钮 - 测试通过\n"}, {"text": "测试按钮: 仪表盘导航\n"}, {"text": "⚠️  发现3个匹配的元素，使用第一个\n"}, {"text": "✅ 仪表盘导航 - 测试通过\n"}, {"text": "测试按钮: 租户管理导航\n"}, {"text": "⚠️  发现4个匹配的元素，使用第一个\n"}, {"text": "✅ 租户管理导航 - 测试通过\n"}, {"text": "测试按钮: 监控导航\n"}, {"text": "✅ 监控导航 - 测试通过\n"}, {"text": "测试按钮: 设置导航\n"}, {"text": "✅ 设置导航 - 测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:56:11.341Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-1e61fe2e9794c45e0823", "file": "admin-buttons-test.spec.js", "line": 248, "column": 3}, {"title": "测试租户管理页面按钮", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 30122, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-buttons-test.spec.js", "column": 20, "line": 290}, "message": "Error: page.goto: Target page, context or browser has been closed\n\n\u001b[0m \u001b[90m 288 |\u001b[39m         \u001b[36mawait\u001b[39m testButton(page\u001b[33m,\u001b[39m button\u001b[33m.\u001b[39mselector\u001b[33m,\u001b[39m button\u001b[33m.\u001b[39mname)\u001b[33m;\u001b[39m\n \u001b[90m 289 |\u001b[39m         \u001b[90m// 返回到租户页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 290 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/tenants`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 291 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 292 |\u001b[39m       }\n \u001b[90m 293 |\u001b[39m     }\u001b[0m\n\u001b[2m    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-buttons-test.spec.js:290:20\u001b[22m"}], "stdout": [{"text": "测试按钮: 创建租户按钮\n"}, {"text": "✅ 创建租户按钮 - 测试通过\n"}, {"text": "测试按钮: 全选按钮\n"}, {"text": "✅ 全选按钮 - 测试通过\n"}, {"text": "测试按钮: 批量暂停按钮\n"}, {"text": "测试按钮: 批量激活按钮\n"}, {"text": "测试按钮: 导出数据按钮\n"}, {"text": "\n=== 测试报告摘要 ===\n"}, {"text": "总测试数: 11\n"}, {"text": "通过: 9\n"}, {"text": "失败: 2\n"}, {"text": "测试时长: 56729ms\n"}, {"text": "报告已保存到: admin-buttons-test-report.html\n"}], "stderr": [{"text": "❌ 批量暂停按钮 - 测试失败: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.btn-group button[onclick=\"batchUpdateTenantStatus(\\'suspended\\')\"]')\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"button\" class=\"btn btn-outline-warning disabled\" onclick=\"batchUpdateTenantStatus('suspended')\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    19 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n"}, {"text": "❌ 批量激活按钮 - 测试失败: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.btn-group button[onclick=\"batchUpdateTenantStatus(\\'active\\')\"]')\u001b[22m\n\u001b[2m    - locator resolved to <button disabled type=\"button\" class=\"btn btn-outline-success disabled\" onclick=\"batchUpdateTenantStatus('active')\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    19 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n"}, {"text": "❌ 导出数据按钮 - 测试失败: page.waitForTimeout: Target page, context or browser has been closed\n"}, {"text": "截图失败: page.screenshot: Target page, context or browser has been closed\n"}], "retry": 0, "startTime": "2025-08-27T06:56:24.370Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-buttons-test-后台管理中心按钮功能测试-测试租户管理页面按钮-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-buttons-test-后台管理中心按钮功能测试-测试租户管理页面按钮-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "825f1497d54556fc4c4f-9ed224240671b724bb6e", "file": "admin-buttons-test.spec.js", "line": 270, "column": 3}, {"title": "测试创建租户页面按钮", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 2571, "errors": [], "stdout": [{"text": "开始测试后台管理系统按钮功能...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:56:57.197Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-050a0496eb705b1b4e38", "file": "admin-buttons-test.spec.js", "line": 312, "column": 3}, {"title": "测试订阅管理页面按钮", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 631, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:57:02.403Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-71020e8146a0d5410576", "file": "admin-buttons-test.spec.js", "line": 336, "column": 3}, {"title": "测试JavaScript函数调用", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 3653, "errors": [], "stdout": [{"text": "❌ JS函数-全选租户函数 - 函数不存在\n"}, {"text": "❌ JS函数-切换全选函数 - 函数不存在\n"}, {"text": "❌ JS函数-获取选中租户ID函数 - 函数不存在\n"}, {"text": "❌ JS函数-批量更新状态函数 - 函数不存在\n"}, {"text": "❌ JS函数-导出租户函数 - 函数不存在\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:57:03.062Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-624c95054e67bfcd0e46", "file": "admin-buttons-test.spec.js", "line": 367, "column": 3}, {"title": "测试控制台错误检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 10354, "errors": [], "stdout": [{"text": "\n=== 测试报告摘要 ===\n"}, {"text": "总测试数: 9\n"}, {"text": "通过: 4\n"}, {"text": "失败: 5\n"}, {"text": "测试时长: 19914ms\n"}, {"text": "报告已保存到: admin-buttons-test-report.html\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:57:06.727Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-45d6d47b8f5f63b76e5d", "file": "admin-buttons-test.spec.js", "line": 420, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-27T06:55:57.348Z", "duration": 81785.21500000001, "expected": 7, "skipped": 0, "unexpected": 1, "flaky": 0}}