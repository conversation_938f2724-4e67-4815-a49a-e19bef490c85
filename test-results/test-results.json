{"config": {"configFile": "/Volumes/DATA/千问/智慧养鹅全栈/playwright.config.js", "rootDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-setup.js", "globalTeardown": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 4, "webServer": null}, "suites": [{"title": "admin-buttons-test.spec.js", "file": "admin-buttons-test.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "后台管理中心按钮功能测试", "file": "admin-buttons-test.spec.js", "line": 190, "column": 6, "specs": [{"title": "访问登录页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4294, "errors": [], "stdout": [{"text": "开始测试后台管理系统按钮功能...\n"}, {"text": "测试按钮: 访问登录页面\n"}, {"text": "✅ 访问登录页面 - 测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:47:58.005Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-13fe31ce9fc7dccd15d9", "file": "admin-buttons-test.spec.js", "line": 215, "column": 3}, {"title": "测试登录功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2686, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:48:06.839Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-1064d3bc3f5a054e3e11", "file": "admin-buttons-test.spec.js", "line": 222, "column": 3}, {"title": "测试导航菜单按钮", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 12778, "errors": [], "stdout": [{"text": "测试按钮: 侧边栏切换按钮\n"}, {"text": "✅ 侧边栏切换按钮 - 测试通过\n"}, {"text": "测试按钮: 仪表盘导航\n"}, {"text": "⚠️  发现3个匹配的元素，使用第一个\n"}, {"text": "✅ 仪表盘导航 - 测试通过\n"}, {"text": "测试按钮: 租户管理导航\n"}, {"text": "⚠️  发现4个匹配的元素，使用第一个\n"}, {"text": "✅ 租户管理导航 - 测试通过\n"}, {"text": "测试按钮: 监控导航\n"}, {"text": "✅ 监控导航 - 测试通过\n"}, {"text": "测试按钮: 设置导航\n"}, {"text": "✅ 设置导航 - 测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:48:09.540Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-1e61fe2e9794c45e0823", "file": "admin-buttons-test.spec.js", "line": 248, "column": 3}, {"title": "测试租户管理页面按钮", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 27534, "errors": [], "stdout": [{"text": "测试按钮: 创建租户按钮\n"}, {"text": "✅ 创建租户按钮 - 测试通过\n"}, {"text": "测试按钮: 全选按钮\n"}, {"text": "✅ 全选按钮 - 测试通过\n"}, {"text": "测试按钮: 批量暂停按钮\n"}, {"text": "✅ 批量暂停按钮 - 测试通过\n"}, {"text": "测试按钮: 批量激活按钮\n"}, {"text": "✅ 批量激活按钮 - 测试通过\n"}, {"text": "测试按钮: 导出数据按钮\n"}, {"text": "✅ 导出数据按钮 - 测试通过\n"}, {"text": "测试按钮: 订阅管理按钮\n"}, {"text": "✅ 订阅管理按钮 - 测试通过\n"}, {"text": "测试按钮: 使用统计按钮\n"}, {"text": "✅ 使用统计按钮 - 测试通过\n"}, {"text": "测试按钮: 查看详情按钮(第一个)\n"}, {"text": "⚠️  发现3个匹配的元素，使用第一个\n"}, {"text": "✅ 查看详情按钮(第一个) - 测试通过\n"}, {"text": "测试按钮: 编辑按钮(第一个)\n"}, {"text": "⚠️  发现3个匹配的元素，使用第一个\n"}, {"text": "✅ 编辑按钮(第一个) - 测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:48:22.325Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-9ed224240671b724bb6e", "file": "admin-buttons-test.spec.js", "line": 270, "column": 3}, {"title": "测试创建租户页面按钮", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8993, "errors": [], "stdout": [{"text": "测试按钮: 取消按钮\n"}, {"text": "✅ 取消按钮 - 测试通过\n"}, {"text": "测试按钮: 创建租户提交按钮\n"}, {"text": "✅ 创建租户提交按钮 - 测试通过\n"}, {"text": "测试按钮: 返回列表按钮\n"}, {"text": "✅ 返回列表按钮 - 测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:48:49.866Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-050a0496eb705b1b4e38", "file": "admin-buttons-test.spec.js", "line": 312, "column": 3}, {"title": "测试订阅管理页面按钮", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 13765, "errors": [], "stdout": [{"text": "测试按钮: 计划筛选下拉框\n"}, {"text": "✅ 计划筛选下拉框 - 测试通过\n"}, {"text": "测试按钮: 状态筛选下拉框\n"}, {"text": "✅ 状态筛选下拉框 - 测试通过\n"}, {"text": "测试按钮: 续费按钮(第一个)\n"}, {"text": "⚠️  发现3个匹配的元素，使用第一个\n"}, {"text": "✅ 续费按钮(第一个) - 测试通过\n"}, {"text": "测试按钮: 变更订阅按钮(第一个)\n"}], "stderr": [{"text": "❌ 变更订阅按钮(第一个) - 测试失败: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button[onclick*=\"changeSubscription\"]:first-child') to be visible\u001b[22m\n\n"}], "retry": 0, "startTime": "2025-08-27T06:48:58.871Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-71020e8146a0d5410576", "file": "admin-buttons-test.spec.js", "line": 336, "column": 3}, {"title": "测试JavaScript函数调用", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3883, "errors": [], "stdout": [{"text": "❌ JS函数-全选租户函数 - 测试失败: page.evaluate: ReferenceError: selectAllTenants is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n"}, {"text": "❌ JS函数-切换全选函数 - 测试失败: page.evaluate: ReferenceError: toggleSelectAllTenants is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n"}, {"text": "❌ JS函数-获取选中租户ID函数 - 测试失败: page.evaluate: ReferenceError: getSelectedTenantIds is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n"}, {"text": "❌ JS函数-批量更新状态函数 - 测试失败: page.evaluate: ReferenceError: batchUpdateTenantStatus is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n"}, {"text": "❌ JS函数-导出租户函数 - 测试失败: page.evaluate: ReferenceError: exportTenants is not defined\n    at eval (eval at <anonymous> (eval at evaluate (:291:30)), <anonymous>:1:1)\n    at eval (eval at evaluate (:291:30), <anonymous>:3:67)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:49:12.645Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-624c95054e67bfcd0e46", "file": "admin-buttons-test.spec.js", "line": 367, "column": 3}, {"title": "测试控制台错误检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 11007, "errors": [], "stdout": [{"text": "\n=== 测试报告摘要 ===\n"}, {"text": "总测试数: 32\n"}, {"text": "通过: 26\n"}, {"text": "失败: 6\n"}, {"text": "测试时长: 89576ms\n"}, {"text": "报告已保存到: admin-buttons-test-report.html\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-27T06:49:16.539Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "825f1497d54556fc4c4f-45d6d47b8f5f63b76e5d", "file": "admin-buttons-test.spec.js", "line": 414, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-27T06:47:57.515Z", "duration": 91390.446, "expected": 8, "skipped": 0, "unexpected": 0, "flaky": 0}}